import 'package:thinking_analytics/td_analytics.dart';

class SqTrackManager {
  static final _taAppId = '';
  static final _taServerUrl = '';

  static bool _isInit = false;

  //单例生命
  factory SqTrackManager() => _instance;

  SqTrackManager._internal();

  static final SqTrackManager _instance = SqTrackManager._internal();

  /// 隐私授权之后调用
  static Future<void> init() async {
    if (_isInit) {
      return;
    }
    _isInit = true;
    //SDK初始化
    await TDAnalytics.init(_taAppId, _taServerUrl);
    TDAnalytics.setSuperProperties({});
    // 设置动态公共属性, 动态公共属性不支持自动采集事件
    TDAnalytics.setDynamicSuperProperties(() {
      return _getDynamicSuperProperties();
    });
  }

  ///动态公共属性
  static Map<String, dynamic> _getDynamicSuperProperties() {
    return <String, dynamic>{'DYNAMIC_DATE': DateTime.now().toUtc()};
  }

  static SqTrackManager getInstance() {
    return _instance;
  }

  ///设置账号ID
  void setUserId(String userId) {
    TDAnalytics.login(userId);
  }

  void track(String eventName, [Map<String, dynamic>? properties]) {
    TDAnalytics.track(eventName, properties: properties);
  }
}

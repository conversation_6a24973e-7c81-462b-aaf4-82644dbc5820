import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class TimeTrackingPage extends StatefulWidget {
  const TimeTrackingPage({super.key});

  @override
  State<TimeTrackingPage> createState() => _TimeTrackingPageState();
}

class _TimeTrackingPageState extends State<TimeTrackingPage> {
  List<WorkRecord> _workRecords = [];
  bool _isClockedIn = false;
  DateTime? _clockInTime;
  
  @override
  void initState() {
    super.initState();
    _loadWorkRecords();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('工时记录'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildClockSection(),
          _buildStatsSection(),
          Expanded(child: _buildRecordsList()),
        ],
      ),
    );
  }

  Widget _buildClockSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        children: [
          Text(
            _getCurrentTimeString(),
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _isClockedIn ? null : _clockIn,
                icon: const Icon(Icons.login),
                label: const Text('上班打卡'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              ElevatedButton.icon(
                onPressed: !_isClockedIn ? null : _clockOut,
                icon: const Icon(Icons.logout),
                label: const Text('下班打卡'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          if (_isClockedIn && _clockInTime != null)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Text(
                '已打卡: ${_formatTime(_clockInTime!)}',
                style: TextStyle(color: Colors.green[700], fontWeight: FontWeight.w500),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    final currentMonth = DateTime.now();
    final monthlyRecords = _getMonthlyRecords(currentMonth);
    final avgHours = _calculateAverageHours(monthlyRecords);
    final totalHours = _calculateTotalHours(monthlyRecords);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('本月总工时', '${totalHours.toStringAsFixed(1)}h'),
          _buildStatItem('工作天数', '${monthlyRecords.length}天'),
          _buildStatItem('平均工时', '${avgHours.toStringAsFixed(1)}h'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 4),
        Text(value, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
      ],
    );
  }

  Widget _buildRecordsList() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('工时记录', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: _workRecords.length,
              itemBuilder: (context, index) {
                final record = _workRecords[_workRecords.length - 1 - index];
                return _buildRecordItem(record);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordItem(WorkRecord record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue[100],
          child: Text('${record.date.day}', style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
        title: Text(_formatDate(record.date)),
        subtitle: Text('${_formatTime(record.clockIn)} - ${_formatTime(record.clockOut)}'),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text('${record.workHours.toStringAsFixed(1)}h', 
                 style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            Text('午休${record.lunchBreakHours}h', 
                 style: const TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  void _clockIn() {
    setState(() {
      _isClockedIn = true;
      _clockInTime = DateTime.now();
    });
  }

  void _clockOut() {
    if (_clockInTime == null) return;
    
    final clockOutTime = DateTime.now();
    final workRecord = WorkRecord(
      date: DateTime.now(),
      clockIn: _clockInTime!,
      clockOut: clockOutTime,
      lunchBreakHours: 1.5, // 默认午休1.5小时
    );
    
    setState(() {
      _workRecords.add(workRecord);
      _isClockedIn = false;
      _clockInTime = null;
    });
    
    _saveWorkRecords();
  }

  String _getCurrentTimeString() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  List<WorkRecord> _getMonthlyRecords(DateTime month) {
    return _workRecords.where((record) => 
        record.date.year == month.year && record.date.month == month.month).toList();
  }

  double _calculateAverageHours(List<WorkRecord> records) {
    if (records.isEmpty) return 0.0;
    final totalHours = records.fold(0.0, (sum, record) => sum + record.workHours);
    return totalHours / records.length;
  }

  double _calculateTotalHours(List<WorkRecord> records) {
    return records.fold(0.0, (sum, record) => sum + record.workHours);
  }

  Future<void> _loadWorkRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = prefs.getString('work_records') ?? '[]';
    final recordsList = json.decode(recordsJson) as List;
    
    setState(() {
      _workRecords = recordsList.map((json) => WorkRecord.fromJson(json)).toList();
    });
  }

  Future<void> _saveWorkRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = json.encode(_workRecords.map((record) => record.toJson()).toList());
    await prefs.setString('work_records', recordsJson);
  }
}

class WorkRecord {
  final DateTime date;
  final DateTime clockIn;
  final DateTime clockOut;
  final double lunchBreakHours;

  WorkRecord({
    required this.date,
    required this.clockIn,
    required this.clockOut,
    required this.lunchBreakHours,
  });

  double get workHours {
    final totalHours = clockOut.difference(clockIn).inMinutes / 60.0;
    return totalHours - lunchBreakHours;
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.millisecondsSinceEpoch,
      'clockIn': clockIn.millisecondsSinceEpoch,
      'clockOut': clockOut.millisecondsSinceEpoch,
      'lunchBreakHours': lunchBreakHours,
    };
  }

  factory WorkRecord.fromJson(Map<String, dynamic> json) {
    return WorkRecord(
      date: DateTime.fromMillisecondsSinceEpoch(json['date']),
      clockIn: DateTime.fromMillisecondsSinceEpoch(json['clockIn']),
      clockOut: DateTime.fromMillisecondsSinceEpoch(json['clockOut']),
      lunchBreakHours: json['lunchBreakHours'].toDouble(),
    );
  }
}
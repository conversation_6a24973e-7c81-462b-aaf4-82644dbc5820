import 'package:dlyz_flutter/utils/aes_utils.dart';
import 'package:dlyz_flutter/utils/md5_utils.dart';
import 'package:dlyz_flutter/utils/sp_utils.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'bugless/app_error_handler.dart';
import 'webview/webview.dart';

void main() async {
  AppErrorHandler.run(const MyApp(), initFunc: () async {
    await initApp();
  });
}

///初始化
Future<void> initApp() async {
  await SpManager.init();
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WebViewPage(
                      title: 'WebView Demo',
                    ),
                  ),
                );
              },
              child: const Text('打开WebView演示'),
            ),
            ...spDemoLayout(),
            ...encryptDemoLayout(),
            ...md5DemoLayout(),
            ...errorDemoLayout(),
            ...tabPageDemoLayout()
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  ///sp demo
  List spDemoLayout() {
    return  [
        ElevatedButton(
          onPressed: () {
            SpManager.getInstance().put("counter", '$_counter');
          },
          child: const Text('存储sp'),
        ),
        ElevatedButton(
          onPressed: () {
            var toastMsg = SpManager.getInstance().getString("counter") ?? "null";
            Fluttertoast.showToast(
                msg: toastMsg,
                toastLength: Toast.LENGTH_SHORT, // 或者Toast.LENGTH_LONG
                gravity: ToastGravity.CENTER,    // 位置：可以调整为TOP、CENTER等
                timeInSecForIosWeb: 1,           // iOS和Web上显示的时间（秒）
                backgroundColor: Colors.grey,
                textColor: Colors.white,
                fontSize: 16.0
            );
          },
          child: const Text('获取sp'),
        )
      ];
  }

  ///加解密demo
  List encryptDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          SpManager.getInstance().put("counter", AesUtil.encrypt('$_counter'));
        },
        child: const Text('AES加密'),
      ),

      ElevatedButton(
        onPressed: () {
          var decrypted = AesUtil.decrypt(SpManager.getInstance().getString('counter') ?? "");
          Fluttertoast.showToast(
              msg: decrypted,
              toastLength: Toast.LENGTH_SHORT, // 或者Toast.LENGTH_LONG
              gravity: ToastGravity.CENTER,    // 位置：可以调整为TOP、CENTER等
              timeInSecForIosWeb: 1,           // iOS和Web上显示的时间（秒）
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0
          );
        },
        child: const Text('AES解密'),
      ),
    ];
  }

  ///md5 demo
  List md5DemoLayout() {
    return [
      ElevatedButton(
          onPressed: () {
            var md5 = Md5Utils.generateMd5('$_counter');
            Fluttertoast.showToast(
                msg: md5,
                toastLength: Toast.LENGTH_SHORT, // 或者Toast.LENGTH_LONG
                gravity: ToastGravity.CENTER,    // 位置：可以调整为TOP、CENTER等
                timeInSecForIosWeb: 1,           // iOS和Web上显示的时间（秒）
                backgroundColor: Colors.grey,
                textColor: Colors.white,
                fontSize: 16.0
            );
          },
        child: const Text('MD5加密'),
      )
    ];
  }

  ///异常demo
  List errorDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          throw Exception('测试异常');
        },
        child: const Text('测试异常'),
      )
    ];
  }

  ///tab+page联动
  List tabPageDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const WebViewPage(
                title: 'WebView Demo',
              ),
            ),
          );
        },
        child: const Text('跳转到tabPage'),
      )
    ];
  }
}

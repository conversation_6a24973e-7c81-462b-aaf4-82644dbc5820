import 'dart:async';
import 'package:dlyz_flutter/bugless/bugless.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppErrorHandler {
  static void run(Widget app, {Future<void> Function()? initFunc}) {
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      var errorContent =
          'message = ${details.exception}, stack = ${details.stack}';
      debugPrint(errorContent);
      _reportError(errorContent);
    };

    runZonedGuarded(
      () async {
        WidgetsFlutterBinding.ensureInitialized();
        if (initFunc != null) {
          await initFunc();
        }
        runApp(app);
      },
      (Object error, StackTrace stack) {
        var errorContent = 'message = $error, stack = $stack';
        debugPrint(errorContent);
        _reportError(errorContent);
      },
    );
  }

  static Future<void> _reportError(String msg) async {
    PackageInfo info = await PackageInfo.fromPlatform();
    var title = '${info.packageName}-${info.version}-CRASH';
    // await Bugless().crashReport(
    //   1,
    //   title,
    //   msg,
    //   DateTime.now().millisecondsSinceEpoch,
    // );
  }
}

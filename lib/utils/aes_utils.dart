import 'package:encrypt/encrypt.dart';

enum AesMode { CBC, ECB }

enum AesKeySize { AES128, AES256 }

class AesUtil {
  /// 默认Key和IV
  static const String _defaultKey128 = '2384039739279483';
  static const String _defaultKey256 = '13CrHshctrmwCn3JOIPaSNMPjssUlmPn';
  static const String _defaultIv = 'iBPCWyM0EmkJeqDF';

  /// AES 加密
  static String encrypt(
    String plainText, {
    AesMode mode = AesMode.CBC,
    AesKeySize keySize = AesKeySize.AES128,
    String? key,
    String? iv,
  }) {
    final aesKey = _getKey(keySize, key);
    final aesIv = _getIv(mode, iv);

    final encrypter = Encrypter(_getAes(aesKey, mode));
    final encrypted =
        mode == AesMode.CBC
            ? encrypter.encrypt(plainText, iv: aesIv)
            : encrypter.encrypt(plainText);

    return encrypted.base64;
  }

  /// AES 解密
  static String decrypt(
    String encryptedBase64, {
    AesMode mode = AesMode.CBC,
    AesKeySize keySize = AesKeySize.AES128,
    String? key,
    String? iv,
  }) {
    final aesKey = _getKey(keySize, key);
    final aesIv = _getIv(mode, iv);

    final encrypter = Encrypter(_getAes(aesKey, mode));
    final encrypted = Encrypted.from64(encryptedBase64);

    try {
      var decrypted =
          mode == AesMode.CBC
              ? encrypter.decrypt(encrypted, iv: aesIv)
              : encrypter.decrypt(encrypted);
      return decrypted;
    } catch (e) {
      return "";
    }
  }

  /// 内部方法：获取 AES Key
  static Key _getKey(AesKeySize keySize, String? customKey) {
    String keyStr =
        customKey ??
        (keySize == AesKeySize.AES256 ? _defaultKey256 : _defaultKey128);
    return Key.fromUtf8(
      keyStr.padRight(_keyLength(keySize)).substring(0, _keyLength(keySize)),
    );
  }

  /// 内部方法：获取 IV（ECB 不需要 IV）
  static IV _getIv(AesMode mode, String? customIv) {
    if (mode == AesMode.ECB) return IV.fromLength(16); // ECB 不使用 IV，传空即可
    final ivStr = customIv ?? _defaultIv;
    return IV.fromUtf8(ivStr.padRight(16).substring(0, 16));
  }

  /// 获取 Key 长度（单位：字节）
  static int _keyLength(AesKeySize size) => size == AesKeySize.AES256 ? 32 : 16;

  /// 获取 AES 实例
  static AES _getAes(Key key, AesMode mode) {
    return AES(
      key,
      mode: mode == AesMode.CBC ? AESMode.cbc : AESMode.ecb,
      padding: 'PKCS7',
    );
  }
}

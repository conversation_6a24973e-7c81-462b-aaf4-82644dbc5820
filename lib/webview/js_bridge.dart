import 'dart:convert';
import 'package:flutter/foundation.dart';

/// JavaScript消息类型
enum JSMessageType {
  /// 页面准备就绪
  ready,
  /// 用户交互事件
  userAction,
  /// 数据请求
  dataRequest,
  /// 页面导航
  navigation,
  /// 自定义消息
  custom,
}

/// JavaScript消息结构
class JSMessage {
  final JSMessageType type;
  final String method;
  final Map<String, dynamic> data;
  final String? id;

  JSMessage({
    required this.type,
    required this.method,
    required this.data,
    this.id,
  });

  factory JSMessage.fromJson(Map<String, dynamic> json) {
    return JSMessage(
      type: _parseMessageType(json['type']),
      method: json['method'] ?? '',
      data: json['data'] ?? {},
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'method': method,
      'data': data,
      if (id != null) 'id': id,
    };
  }

  static JSMessageType _parseMessageType(dynamic type) {
    if (type == null) return JSMessageType.custom;
    
    switch (type.toString().toLowerCase()) {
      case 'ready':
        return JSMessageType.ready;
      case 'useraction':
        return JSMessageType.userAction;
      case 'datarequest':
        return JSMessageType.dataRequest;
      case 'navigation':
        return JSMessageType.navigation;
      default:
        return JSMessageType.custom;
    }
  }
}

/// JavaScript通信桥接器
class JSBridge {
  /// 消息处理器映射
  final Map<String, Function(JSMessage)> _handlers = {};
  
  /// 通用消息监听器
  Function(JSMessage)? onMessage;

  /// 注册消息处理器
  void registerHandler(String method, Function(JSMessage) handler) {
    _handlers[method] = handler;
  }

  /// 移除消息处理器
  void unregisterHandler(String method) {
    _handlers.remove(method);
  }

  /// 处理来自WebView的消息
  void handleMessage(String message) {
    try {
      final Map<String, dynamic> messageMap = jsonDecode(message);
      final jsMessage = JSMessage.fromJson(messageMap);
      
      // 首先尝试特定的处理器
      final handler = _handlers[jsMessage.method];
      if (handler != null) {
        handler(jsMessage);
        return;
      }

      // 如果没有特定处理器，调用通用监听器
      if (onMessage != null) {
        onMessage!(jsMessage);
      } else {
        // 默认处理逻辑
        _handleDefaultMessage(jsMessage);
      }
    } catch (e) {
      debugPrint('解析JavaScript消息失败: $e');
      debugPrint('原始消息: $message');
    }
  }

  /// 默认消息处理逻辑
  void _handleDefaultMessage(JSMessage message) {
    switch (message.type) {
      case JSMessageType.ready:
        debugPrint('WebView已准备就绪: ${message.data}');
        break;
      case JSMessageType.userAction:
        debugPrint('用户操作: ${message.method}, 数据: ${message.data}');
        break;
      case JSMessageType.dataRequest:
        debugPrint('数据请求: ${message.method}, 参数: ${message.data}');
        break;
      case JSMessageType.navigation:
        debugPrint('页面导航: ${message.method}, 目标: ${message.data}');
        break;
      case JSMessageType.custom:
        debugPrint('自定义消息: ${message.method}, 数据: ${message.data}');
        break;
    }
  }

  /// 创建响应消息
  Map<String, dynamic> createResponse({
    required String method,
    required Map<String, dynamic> data,
    String? messageId,
    bool success = true,
  }) {
    return {
      'type': 'response',
      'method': method,
      'data': data,
      'success': success,
      if (messageId != null) 'responseId': messageId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 创建错误响应
  Map<String, dynamic> createErrorResponse({
    required String method,
    required String error,
    String? messageId,
  }) {
    return createResponse(
      method: method,
      data: {'error': error},
      messageId: messageId,
      success: false,
    );
  }
}

/// 预定义的常用JavaScript桥接器
class CommonJSBridge extends JSBridge {
  CommonJSBridge() {
    _setupCommonHandlers();
  }

  void _setupCommonHandlers() {
    // 页面标题更改
    registerHandler('setTitle', (message) {
      final title = message.data['title'] as String?;
      if (title != null) {
        onTitleChanged?.call(title);
      }
    });

    // 页面跳转
    registerHandler('navigate', (message) {
      final url = message.data['url'] as String?;
      if (url != null) {
        onNavigate?.call(url);
      }
    });

    // 显示提示信息
    registerHandler('showToast', (message) {
      final text = message.data['text'] as String?;
      if (text != null) {
        onShowToast?.call(text);
      }
    });

    // 获取设备信息
    registerHandler('getDeviceInfo', (message) {
      onGetDeviceInfo?.call(message.id);
    });

    // 关闭页面
    registerHandler('closePage', (message) {
      onClosePage?.call();
    });
  }

  /// 标题更改回调
  Function(String title)? onTitleChanged;

  /// 页面导航回调
  Function(String url)? onNavigate;

  /// 显示提示回调
  Function(String message)? onShowToast;

  /// 获取设备信息回调
  Function(String? messageId)? onGetDeviceInfo;

  /// 关闭页面回调
  VoidCallback? onClosePage;
} 
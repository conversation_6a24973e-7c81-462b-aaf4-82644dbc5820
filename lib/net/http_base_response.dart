import 'package:dlyz_flutter/net/http_error_code.dart';

class BaseResponse<T> {
  final int code;
  final String message;
  final T? data;

  BaseResponse({required this.code, required this.message, this.data});

  bool get success => code == 200;

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return BaseResponse<T>(
      code: json['code'] ?? ErrorCode.unknown,
      message: json['message'] ?? '',
      data: json['data'] != null ? fromJsonT(json['data']) : null,
    );
  }

  factory BaseResponse.error(String message, {int code = ErrorCode.unknown}) {
    return BaseResponse<T>(code: code, message: message, data: null);
  }
}

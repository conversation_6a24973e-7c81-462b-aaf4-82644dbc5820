import 'package:dio/dio.dart';
import 'http_base_response.dart';
import 'http_error_code.dart';
import 'http_net_exception.dart';

class HttpService {
  static final HttpService _instance = HttpService._internal();

  factory HttpService() => _instance;

  final Map<String, Dio> _dioCache = {};

  HttpService._internal();

  static HttpService getInstance() {
    return _instance;
  }

  Dio _getDio(String baseUrl) {
    if (_dioCache.containsKey(baseUrl)) return _dioCache[baseUrl]!;

    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {'Content-Type': 'application/json'},
      ),
    );

    dio.interceptors.add(LogInterceptor(responseBody: true));
    _dioCache[baseUrl] = dio;
    return dio;
  }

  Future<BaseResponse<T>> get<T>(
    String path, {
    required String baseUrl,
    Map<String, dynamic>? queryParameters,
    required T Function(dynamic) fromJsonT,
  }) async {
    try {
      final response = await _getDio(
        baseUrl,
      ).get(path, queryParameters: queryParameters);
      return _handleResponse(response, fromJsonT);
    } on DioException catch (e) {
      return handleDioError<T>(e);
    } catch (e) {
      return BaseResponse.error('未知错误: $e', code: ErrorCode.unknown);
    }
  }

  Future<BaseResponse<T>> post<T>(
    String path, {
    required String baseUrl,
    Map<String, dynamic>? data,
    required T Function(dynamic) fromJsonT,
  }) async {
    try {
      final response = await _getDio(baseUrl).post(path, data: data);
      return _handleResponse(response, fromJsonT);
    } on DioException catch (e) {
      return handleDioError<T>(e);
    } catch (e) {
      return BaseResponse.error('未知错误: $e', code: ErrorCode.unknown);
    }
  }

  BaseResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic json) fromJsonT,
  ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      try {
        final parsedData = fromJsonT(response.data);
        return BaseResponse<T>(code: 200, message: 'OK', data: parsedData);
      } catch (e) {
        return BaseResponse.error('解析失败: $e', code: ErrorCode.parseError);
      }
    } else {
      return BaseResponse.error(
        'HTTP错误: ${response.statusCode}',
        code: ErrorCode.httpError,
      );
    }
  }
}
